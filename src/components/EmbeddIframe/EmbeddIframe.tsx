import React, {
  useState,
  forwardRef,
} from 'react';
import { Box, CircularProgress } from '@mui/material';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

export interface EmbeddIframeProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  onLoad?: () => void;
  sandbox?: string;
}

const EmbeddIframe = forwardRef<HTMLIFrameElement, EmbeddIframeProps>(({
  src,
  title = '',
  className = 'embedd-iframe-wrap',
  style = {},
  showLoading = true,
  onLoad,
  sandbox = DEFAULT_SANDBOX,
  ...rest
}, ref) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  return (
    <Box
      className={className}
    >
      {isLoading && showLoading && (
        <div data-testid="loader">
          <CircularProgress size={24} />
        </div>
      )}

      <iframe
        ref={ref}
        src={src}
        title={title}
        style={{ ...style }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        {...rest}
      />
    </Box>
  );
});

EmbeddIframe.displayName = 'EmbeddIframe';

export default EmbeddIframe;
