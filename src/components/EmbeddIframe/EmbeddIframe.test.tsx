import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import EmbeddIframe from './EmbeddIframe';

describe('EmbeddIframe', () => {
  const defaultSrc = 'https://example.com/';

  test('should render iframe with provided src and title', () => {
    render(<EmbeddIframe src={defaultSrc} title="Example Frame" />);
    const iframe = screen.getByTitle('Example Frame') as HTMLIFrameElement;

    expect(iframe).toBeInTheDocument();
    expect(iframe.src).toBe(defaultSrc);
  });

  test('should not render Loader if showLoading is false', () => {
    render(<EmbeddIframe src={defaultSrc} title="No Loader Frame" showLoading={false} />);
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
  });

  test('should call onLoad and hide loader when iframe loads', () => {
    const onLoadMock = jest.fn();

    render(<EmbeddIframe src={defaultSrc} title="Load Frame" onLoad={onLoadMock} />);
    const iframe = screen.getByTitle('Load Frame');

    fireEvent.load(iframe);

    expect(onLoadMock).toHaveBeenCalled();
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
  });

  test('should apply default sandbox if none provided', () => {
    render(<EmbeddIframe src={defaultSrc} title="Sandbox Frame" />);
    const iframe = screen.getByTitle('Sandbox Frame');

    expect(iframe.getAttribute('sandbox')).toBe('allow-same-origin allow-scripts allow-forms allow-popups');
  });

  test('should allow overriding sandbox value via props', () => {
    const customSandbox = 'allow-scripts';

    render(
      <EmbeddIframe
        src={defaultSrc}
        title="Custom Sandbox"
        sandbox={customSandbox}
      />,
    );
    const iframe = screen.getByTitle('Custom Sandbox');

    expect(iframe.getAttribute('sandbox')).toBe(customSandbox);
  });

  test('should apply additional props to iframe (like name, data attributes)', () => {
    render(
      <EmbeddIframe
        src={defaultSrc}
        title="Extra Props"
        name="iframe-name"
        data-testid="custom-iframe"
      />,
    );

    const iframe = screen.getByTestId('custom-iframe');

    expect(iframe).toHaveAttribute('name', 'iframe-name');
    expect(iframe).toHaveAttribute('src', defaultSrc);
  });
});
